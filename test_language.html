<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบระบบภาษา - NK Logistics</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        .language-toggle {
            text-align: center;
            margin-bottom: 30px;
        }
        .lang-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .lang-btn.active {
            background: #28a745;
        }
        .lang-btn:hover {
            opacity: 0.8;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h3 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .contact-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .contact-icon {
            width: 40px;
            height: 40px;
            background: #007bff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 18px;
        }
        .contact-text h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        .contact-text p {
            margin: 0;
            color: #666;
        }
        .footer-section {
            margin: 20px 0;
            padding: 20px;
            background: #343a40;
            color: white;
            border-radius: 8px;
        }
        .footer-section h4 {
            color: #fff;
            margin-bottom: 15px;
        }
        .footer-section ul {
            list-style: none;
            padding: 0;
        }
        .footer-section li {
            margin: 8px 0;
            color: #ccc;
        }
        .footer-section a {
            color: #ccc;
            text-decoration: none;
        }
        .footer-section a:hover {
            color: #fff;
        }
        .current-lang {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            margin-bottom: 20px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🌐 ทดสอบระบบภาษา NK Logistics</h1>
        
        <div class="language-toggle">
            <button class="lang-btn active" onclick="switchLanguage('th')" id="btn-th">ไทย</button>
            <button class="lang-btn" onclick="switchLanguage('en')" id="btn-en">English</button>
        </div>

        <div class="current-lang">
            <span id="current-language">ภาษาปัจจุบัน: ไทย</span>
        </div>

        <div class="section">
            <h3>📞 ข้อมูลติดต่อ (Contact Information)</h3>
            
            <div class="contact-item">
                <div class="contact-icon">📞</div>
                <div class="contact-text">
                    <h4 id="contact-phone-label">โทรศัพท์</h4>
                    <p id="contact-phone-number">+66 2-xxx-xxxx</p>
                </div>
            </div>

            <div class="contact-item">
                <div class="contact-icon">📧</div>
                <div class="contact-text">
                    <h4 id="contact-email-label">อีเมล</h4>
                    <p id="contact-email-address"><EMAIL></p>
                </div>
            </div>

            <div class="contact-item">
                <div class="contact-icon">📍</div>
                <div class="contact-text">
                    <h4 id="contact-address-label">ที่อยู่</h4>
                    <p id="contact-full-address">739 ซอยพุทธมณฑลสาย 1 แยก 21 แขวงบางระมาด เขตตลิ่งชัน กทม. 10170</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>🏢 ส่วน Footer (Footer Section)</h3>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div class="footer-section">
                    <h4 id="footer-services-title">บริการ</h4>
                    <ul>
                        <li><a href="#" id="footer-service-domestic">ขนส่งภายในประเทศ</a></li>
                        <li><a href="#" id="footer-service-warehouse">คลังสินค้า</a></li>
                        <li><a href="#" id="footer-service-import-export">นำเข้า-ส่งออก</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 id="footer-contact-title">ติดต่อเรา</h4>
                    <ul>
                        <li id="footer-contact-phone">โทร: +66 2-xxx-xxxx</li>
                        <li id="footer-contact-email">อีเมล: <EMAIL></li>
                        <li id="footer-contact-hours">เวลาทำการ: จ-ศ 8:00-17:00</li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4 id="footer-follow-title">ติดตามเรา</h4>
                    <div style="display: flex; gap: 10px;">
                        <span>📘 Facebook</span>
                        <span>💬 Line</span>
                        <span>📧 Email</span>
                    </div>
                </div>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: #495057; border-radius: 5px;">
                <p id="footer-description" style="margin: 0; text-align: center; color: #fff;">
                    ผู้เชี่ยวชาญด้านการขนส่งและโลจิสติกส์ที่คุณไว้วางใจได้
                </p>
            </div>
        </div>

        <div class="section">
            <h3>✅ การทดสอบ (Testing)</h3>
            <p><strong>วิธีทดสอบ:</strong></p>
            <ol>
                <li>คลิกปุ่ม "ไทย" หรือ "English" ด้านบน</li>
                <li>สังเกตการเปลี่ยนแปลงของข้อความในแต่ละส่วน</li>
                <li>ตรวจสอบว่าข้อมูลติดต่อและ footer เปลี่ยนภาษาถูกต้อง</li>
                <li>ทดสอบสลับภาษาไปมาหลายครั้ง</li>
            </ol>
            
            <p><strong>สิ่งที่ควรเปลี่ยน:</strong></p>
            <ul>
                <li>📞 ป้ายกำกับและข้อมูลติดต่อ</li>
                <li>📧 ข้อมูลอีเมลและป้ายกำกับ</li>
                <li>📍 ที่อยู่ (ไทย/อังกฤษ)</li>
                <li>🏢 หัวข้อและรายการในส่วน footer</li>
                <li>📝 คำอธิบายบริษัท</li>
            </ul>
        </div>
    </div>

    <script>
        let currentLang = 'th';
        let translations = {};

        // โหลดข้อมูลภาษา
        async function loadTranslations() {
            try {
                const response = await fetch('lang.json');
                translations = await response.json();
                console.log('Translations loaded:', translations);
            } catch (error) {
                console.error('Failed to load translations:', error);
            }
        }

        // สลับภาษา
        function switchLanguage(lang) {
            currentLang = lang;
            
            // อัปเดตปุ่ม
            document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`btn-${lang}`).classList.add('active');
            
            // อัปเดตข้อความแสดงภาษาปัจจุบัน
            document.getElementById('current-language').textContent = 
                lang === 'th' ? 'ภาษาปัจจุบัน: ไทย' : 'Current Language: English';
            
            // อัปเดตเนื้อหา
            updateContent(lang);
        }

        // อัปเดตเนื้อหา
        function updateContent(lang) {
            const t = translations[lang];
            if (!t) return;

            // อัปเดตข้อมูลติดต่อ
            const contactElements = {
                'contact-phone-label': t.contact?.phone,
                'contact-email-label': t.contact?.email,
                'contact-address-label': t.contact?.address,
                'contact-phone-number': t.contact?.phone_number,
                'contact-email-address': t.contact?.email_address,
                'contact-full-address': t.contact?.full_address
            };

            // อัปเดต footer
            const footerElements = {
                'footer-description': t.footer?.description,
                'footer-services-title': t.footer?.services_title,
                'footer-contact-title': t.footer?.contact_title,
                'footer-follow-title': t.footer?.follow_title,
                'footer-service-domestic': t.footer?.services?.domestic,
                'footer-service-warehouse': t.footer?.services?.warehouse,
                'footer-service-import-export': t.footer?.services?.import_export,
                'footer-contact-phone': t.footer?.contact_info?.phone,
                'footer-contact-email': t.footer?.contact_info?.email,
                'footer-contact-hours': t.footer?.contact_info?.hours
            };

            // รวมทุก elements
            const allElements = { ...contactElements, ...footerElements };

            // อัปเดตข้อความ
            Object.entries(allElements).forEach(([id, text]) => {
                const element = document.getElementById(id);
                if (element && text) {
                    element.textContent = text;
                }
            });

            console.log(`Language switched to: ${lang}`);
        }

        // เริ่มต้น
        document.addEventListener('DOMContentLoaded', async () => {
            await loadTranslations();
            updateContent(currentLang);
        });
    </script>
</body>
</html>
