// Enhanced NK Logistics Website JavaScript
document.addEventListener("DOMContentLoaded", async () => {
  // Initialize AOS (Animate On Scroll)
  if (typeof AOS !== 'undefined') {
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true,
      offset: 100
    });
  }

  // DOM Elements
  const themeToggle = document.getElementById("themeToggle");
  const langToggle = document.getElementById("langToggle");
  const mobileMenuBtn = document.getElementById("mobileMenuBtn");
  const mainNav = document.getElementById("mainNav");
  const backToTop = document.getElementById("backToTop");
  const contactForm = document.getElementById("contactForm");
  const loadingScreen = document.getElementById("loading-screen");

  // State
  let currentLang = localStorage.getItem("lang") || "th";
  let currentTheme = localStorage.getItem("theme") || "light";

  // Initialize theme
  document.body.classList.toggle("dark", currentTheme === "dark");

  // Load language data
  let langData;
  try {
    langData = await fetch("lang.json").then(res => res.json());
    updateLang(currentLang);
  } catch (error) {
    console.error("Failed to load language data:", error);
  }

  // Hide loading screen
  setTimeout(() => {
    loadingScreen.classList.add('hidden');
  }, 1000);

  // Theme Toggle
  themeToggle.addEventListener("click", () => {
    currentTheme = currentTheme === "light" ? "dark" : "light";
    document.body.classList.toggle("dark", currentTheme === "dark");
    localStorage.setItem("theme", currentTheme);

    // Update theme icon
    const themeIcon = themeToggle.querySelector('.theme-icon');
    themeIcon.textContent = currentTheme === "dark" ? "☀️" : "🌙";
  });

  // Language Toggle
  langToggle.addEventListener("click", () => {
    currentLang = currentLang === "th" ? "en" : "th";
    localStorage.setItem("lang", currentLang);
    updateLang(currentLang);

    // Update language button text
    document.getElementById("langText").textContent = currentLang === "th" ? "EN" : "TH";
  });

  // Mobile Menu Toggle
  mobileMenuBtn.addEventListener("click", () => {
    mobileMenuBtn.classList.toggle("active");
    mainNav.classList.toggle("active");
  });

  // Close mobile menu when clicking on nav links
  document.querySelectorAll('.nav-links a').forEach(link => {
    link.addEventListener('click', () => {
      mobileMenuBtn.classList.remove("active");
      mainNav.classList.remove("active");
    });
  });

  // Smooth scrolling for navigation links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        const headerHeight = document.querySelector('.header').offsetHeight;
        const targetPosition = target.offsetTop - headerHeight;

        window.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });
      }
    });
  });

  // Back to Top Button
  window.addEventListener('scroll', () => {
    if (window.pageYOffset > 300) {
      backToTop.classList.add('visible');
    } else {
      backToTop.classList.remove('visible');
    }
  });

  backToTop.addEventListener('click', () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  });

  // Header scroll effect
  let lastScrollTop = 0;
  const header = document.querySelector('.header');

  window.addEventListener('scroll', () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    if (scrollTop > lastScrollTop && scrollTop > 100) {
      // Scrolling down
      header.style.transform = 'translateY(-100%)';
    } else {
      // Scrolling up
      header.style.transform = 'translateY(0)';
    }

    lastScrollTop = scrollTop;
  });

  // Animated counters for statistics
  function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');

    counters.forEach(counter => {
      const target = parseInt(counter.getAttribute('data-count'));
      const duration = 2000; // 2 seconds
      const increment = target / (duration / 16); // 60fps
      let current = 0;

      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        counter.textContent = Math.floor(current).toLocaleString();
      }, 16);
    });
  }

  // Trigger counter animation when hero section is visible
  const heroSection = document.getElementById('hero');
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        animateCounters();
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.5 });

  if (heroSection) {
    observer.observe(heroSection);
  }

  // Security and validation utilities
  const SecurityUtils = {
    // Input sanitization
    sanitizeInput: (input) => {
      return input.trim().replace(/[<>\"'&]/g, (match) => {
        const entities = {
          '<': '&lt;',
          '>': '&gt;',
          '"': '&quot;',
          "'": '&#x27;',
          '&': '&amp;'
        };
        return entities[match];
      });
    },

    // Validate name
    validateName: (name) => {
      const errors = [];
      if (!name || name.length < 2) {
        errors.push('Name must be at least 2 characters long');
      }
      if (name.length > 100) {
        errors.push('Name must be less than 100 characters');
      }
      if (!/^[a-zA-Zก-๙\s\-\.]+$/u.test(name)) {
        errors.push('Name contains invalid characters');
      }
      return errors;
    },

    // Validate email
    validateEmail: (email) => {
      const errors = [];
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!email) {
        errors.push('Email is required');
      } else if (!emailRegex.test(email)) {
        errors.push('Please enter a valid email address');
      } else if (email.length > 254) {
        errors.push('Email is too long');
      }
      return errors;
    },

    // Validate phone
    validatePhone: (phone) => {
      const errors = [];
      if (phone && phone.trim() !== '') {
        // ตรวจสอบว่าเป็นตัวเลขเท่านั้น
        if (!/^[0-9]+$/.test(phone)) {
          errors.push('Phone number must contain only numbers');
        }
        // ตรวจสอบความยาว
        if (phone.length < 8 || phone.length > 15) {
          errors.push('Phone number must be between 8-15 digits');
        }
      }
      return errors;
    },

    // Validate message
    validateMessage: (message) => {
      const errors = [];
      if (!message || message.length < 10) {
        errors.push('Message must be at least 10 characters long');
      }
      if (message.length > 2000) {
        errors.push('Message must be less than 2000 characters');
      }

      // Check for suspicious patterns
      const suspiciousPatterns = [
        /\b(viagra|cialis|casino|poker|lottery|winner|congratulations)\b/i,
        /<script[^>]*>.*?<\/script>/is,
        /javascript:/i,
        /on\w+\s*=/i
      ];

      for (const pattern of suspiciousPatterns) {
        if (pattern.test(message)) {
          errors.push('Message contains prohibited content');
          break;
        }
      }

      return errors;
    },

    // Check for URLs in message (basic spam protection)
    containsURL: (text) => {
      const urlPattern = /\b(http|https|ftp):\/\/[^\s]+/i;
      return urlPattern.test(text);
    }
  };

  // CSRF Token management
  let csrfToken = null;

  const getCSRFToken = async () => {
    try {
      const response = await fetch('send_mail.php', {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });
      const data = await response.json();
      csrfToken = data.csrf_token;
      return csrfToken;
    } catch (error) {
      console.error('Failed to get CSRF token:', error);
      return null;
    }
  };

  // Initialize CSRF token on page load
  getCSRFToken();

  // Contact Form Enhancement with Security
  if (contactForm) {
    // Real-time validation
    const inputs = contactForm.querySelectorAll('input, textarea');
    inputs.forEach(input => {
      input.addEventListener('blur', () => validateField(input));
      input.addEventListener('input', () => clearFieldError(input));

      // Special handling for phone input - allow only numbers
      if (input.name === 'phone') {
        input.addEventListener('input', (e) => {
          // Remove any non-numeric characters
          const numericValue = e.target.value.replace(/[^0-9]/g, '');
          if (e.target.value !== numericValue) {
            e.target.value = numericValue;
          }
        });

        // Prevent non-numeric key presses
        input.addEventListener('keypress', (e) => {
          // Allow backspace, delete, tab, escape, enter
          if ([8, 9, 27, 13, 46].indexOf(e.keyCode) !== -1 ||
              // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
              (e.keyCode === 65 && e.ctrlKey === true) ||
              (e.keyCode === 67 && e.ctrlKey === true) ||
              (e.keyCode === 86 && e.ctrlKey === true) ||
              (e.keyCode === 88 && e.ctrlKey === true)) {
            return;
          }
          // Ensure that it is a number and stop the keypress
          if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
            e.preventDefault();
          }
        });
      }
    });

    // Form submission handler
    contactForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      const submitBtn = contactForm.querySelector('#form-submit');

      // Clear previous errors
      clearAllErrors();

      // Get form data
      const formData = new FormData(contactForm);
      const name = formData.get('name');
      const email = formData.get('email');
      const phone = formData.get('phone');
      const message = formData.get('message');
      const honeypot = formData.get('website'); // Honeypot field

      // Honeypot check (spam protection)
      if (honeypot && honeypot.trim() !== '') {
        // This is likely a bot, silently fail
        console.log('Honeypot triggered, likely spam');
        const fakeSuccessMessage = currentLang === 'th'
          ? 'ส่งข้อความเรียบร้อยแล้ว ขอบคุณที่ติดต่อเรา!'
          : 'Message sent successfully! Thank you for contacting us!';
        showNotification(fakeSuccessMessage, 'success');
        contactForm.reset();
        return;
      }

      // Client-side validation
      const validationErrors = [];
      validationErrors.push(...SecurityUtils.validateName(name));
      validationErrors.push(...SecurityUtils.validateEmail(email));
      validationErrors.push(...SecurityUtils.validatePhone(phone));
      validationErrors.push(...SecurityUtils.validateMessage(message));

      // Check for URLs in message (spam protection)
      if (SecurityUtils.containsURL(message)) {
        validationErrors.push('URLs are not allowed in messages');
      }

      if (validationErrors.length > 0) {
        showValidationErrors(validationErrors);
        return;
      }

      // Show loading state
      submitBtn.classList.add('loading');
      submitBtn.disabled = true;

      try {
        // Ensure we have a CSRF token
        if (!csrfToken) {
          await getCSRFToken();
        }

        if (!csrfToken) {
          throw new Error('Security token not available');
        }

        // Add CSRF token to form data
        formData.append('csrf_token', csrfToken);

        // Submit form
        const response = await fetch('send_mail.php', {
          method: 'POST',
          body: formData,
          headers: {
            'Accept': 'application/json'
          }
        });

        const result = await response.json();

        if (response.ok && result.success) {
          // Success
          const successMessage = currentLang === 'th'
            ? 'ส่งข้อความเรียบร้อยแล้ว ขอบคุณที่ติดต่อเรา!'
            : 'Message sent successfully! Thank you for contacting us!';
          showNotification(successMessage, 'success');
          contactForm.reset();
          // Get new CSRF token for next submission
          await getCSRFToken();
        } else {
          // Handle specific error cases
          if (response.status === 429) {
            const errorMessage = currentLang === 'th'
              ? 'ส่งข้อความบ่อยเกินไป กรุณารอสักครู่แล้วลองใหม่'
              : 'Too many requests. Please wait a moment and try again.';
            showNotification(errorMessage, 'error');
          } else if (response.status === 403) {
            const errorMessage = currentLang === 'th'
              ? 'เซสชันหมดอายุ กรุณารีเฟรชหน้าเว็บ'
              : 'Session expired. Please refresh the page.';
            showNotification(errorMessage, 'error');
            // Get new CSRF token
            await getCSRFToken();
          } else {
            const errorMessage = result.error || (currentLang === 'th'
              ? 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง'
              : 'An error occurred. Please try again.');
            showNotification(errorMessage, 'error');
          }
        }

      } catch (error) {
        console.error('Form submission error:', error);
        const errorMessage = currentLang === 'th'
          ? 'เกิดข้อผิดพลาดในการเชื่อมต่อ กรุณาลองใหม่อีกครั้ง'
          : 'Connection error. Please try again.';
        showNotification(errorMessage, 'error');
      } finally {
        // Reset button state
        submitBtn.classList.remove('loading');
        submitBtn.disabled = false;
      }
    });
  }

  // Validation helper functions
  function validateField(field) {
    const value = field.value;
    const name = field.name;
    let errors = [];

    switch (name) {
      case 'name':
        errors = SecurityUtils.validateName(value);
        break;
      case 'email':
        errors = SecurityUtils.validateEmail(value);
        break;
      case 'phone':
        errors = SecurityUtils.validatePhone(value);
        break;
      case 'message':
        errors = SecurityUtils.validateMessage(value);
        if (SecurityUtils.containsURL(value)) {
          errors.push('URLs are not allowed in messages');
        }
        break;
    }

    if (errors.length > 0) {
      showFieldError(field, errors[0]);
    } else {
      clearFieldError(field);
    }
  }

  function showFieldError(field, message) {
    clearFieldError(field);
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    errorDiv.style.cssText = `
      color: #dc3545;
      font-size: 0.875rem;
      margin-top: 0.25rem;
      display: block;
    `;
    field.parentNode.appendChild(errorDiv);
    field.style.borderColor = '#dc3545';
  }

  function clearFieldError(field) {
    const errorDiv = field.parentNode.querySelector('.field-error');
    if (errorDiv) {
      errorDiv.remove();
    }
    field.style.borderColor = '';
  }

  function clearAllErrors() {
    const errorDivs = contactForm.querySelectorAll('.field-error');
    errorDivs.forEach(div => div.remove());
    const inputs = contactForm.querySelectorAll('input, textarea');
    inputs.forEach(input => input.style.borderColor = '');
  }

  function showValidationErrors(errors) {
    const errorMessage = currentLang === 'th'
      ? 'กรุณาแก้ไขข้อมูลที่ไม่ถูกต้อง: ' + errors.join(', ')
      : 'Please correct the following errors: ' + errors.join(', ');
    showNotification(errorMessage, 'error');
  }

  // Notification system
  function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Add styles
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      padding: '1rem 1.5rem',
      borderRadius: '8px',
      color: 'white',
      fontWeight: '500',
      zIndex: '10000',
      transform: 'translateX(100%)',
      transition: 'transform 0.3s ease',
      backgroundColor: type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'
    });

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 5 seconds
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 5000);
  }

  // Update language function
  function updateLang(lang) {
    if (!langData || !langData[lang]) return;

    const t = langData[lang];

    // Update content
    const elements = {
      'hero-title': t.hero?.title,
      'hero-subtitle': t.hero?.subtitle,
      'hero-cta': t.hero?.cta,
      'hero-learn-more': lang === 'th' ? 'เรียนรู้เพิ่มเติม' : 'Learn More',
      'form-title': t.form?.title,
      'footer-copy': t.footer?.copyright,

      // Navigation
      'nav-home': t.navigation?.home,
      'nav-services': t.navigation?.services,
      'nav-about': t.navigation?.about,
      'nav-clients': t.navigation?.clients,
      'nav-contact': t.navigation?.contact,

      // Services
      'services-title': t.services?.title,
      'services-subtitle': t.services?.subtitle,
      'service-1-title': t.services?.domestic?.title,
      'service-1-desc': t.services?.domestic?.description,
      'service-2-title': t.services?.warehouse?.title,
      'service-2-desc': t.services?.warehouse?.description,
      'service-3-title': t.services?.import_export?.title,
      'service-3-desc': t.services?.import_export?.description,

      // Why Choose
      'why-choose-title': t.why_choose?.title,
      'why-choose-subtitle': t.why_choose?.subtitle,
      'why-1-title': t.why_choose?.experience?.title,
      'why-1-desc': t.why_choose?.experience?.description,
      'why-2-title': t.why_choose?.one_stop?.title,
      'why-2-desc': t.why_choose?.one_stop?.description,
      'why-3-title': t.why_choose?.tracking?.title,
      'why-3-desc': t.why_choose?.tracking?.description,
      'why-4-title': t.why_choose?.safety?.title,
      'why-4-desc': t.why_choose?.safety?.description,
      'why-5-title': t.why_choose?.customer_centric?.title,
      'why-5-desc': t.why_choose?.customer_centric?.description,

      // About
      'about-title': t.about?.title,
      'about-desc': t.about?.description,
      'feature-1': t.about?.features?.experience,
      'feature-2': t.about?.features?.professional,
      'feature-3': t.about?.features?.technology,
      'feature-4': t.about?.features?.service,

      // Clients
      'clients-title': t.clients?.title,
      'clients-subtitle': t.clients?.subtitle,

      // Contact
      'contact-desc': t.form?.description,
      'contact-phone-label': t.contact?.phone,
      'contact-email-label': t.contact?.email,
      'contact-address-label': t.contact?.address,

      // Stats
      'stat-clients': t.stats?.clients,
      'stat-years': t.stats?.years,
      'stat-shipments': t.stats?.shipments
    };

    // Update text content
    Object.entries(elements).forEach(([id, text]) => {
      const element = document.getElementById(id);
      if (element && text) {
        element.textContent = text;
      }
    });

    // Update form placeholders and labels
    const formElements = {
      'form-name': t.form?.name,
      'form-email': t.form?.email,
      'form-phone': t.form?.phone,
      'form-message': t.form?.message
    };

    Object.entries(formElements).forEach(([id, placeholder]) => {
      const element = document.getElementById(id);
      if (element && placeholder) {
        element.placeholder = placeholder;
      }
    });

    // Update form labels
    const labelElements = {
      'form-name-label': t.form?.name,
      'form-email-label': t.form?.email,
      'form-phone-label': t.form?.phone,
      'form-message-label': t.form?.message
    };

    Object.entries(labelElements).forEach(([id, text]) => {
      const element = document.getElementById(id);
      if (element && text) {
        element.textContent = text;
      }
    });

    // Update submit button
    const submitBtn = document.querySelector('#form-submit .btn-text');
    if (submitBtn && t.form?.submit) {
      submitBtn.textContent = t.form.submit;
    }

    // Update rate limit notice
    const rateLimitNotice = document.getElementById('rate-limit-notice');
    if (rateLimitNotice) {
      rateLimitNotice.textContent = lang === 'th'
        ? 'เพื่อป้องกันสแปม เราจำกัดการส่งข้อความไม่เกิน 5 ครั้งต่อชั่วโมง'
        : 'To prevent spam, we limit message submissions to 5 times per hour';
    }

    // Update HTML lang attribute
    document.documentElement.lang = lang;
  }

  // Initialize language button text
  document.getElementById("langText").textContent = currentLang === "th" ? "EN" : "TH";

  // Initialize theme icon
  const themeIcon = themeToggle.querySelector('.theme-icon');
  themeIcon.textContent = currentTheme === "dark" ? "☀️" : "🌙";
});