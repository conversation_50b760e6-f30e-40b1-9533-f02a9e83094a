/* CSS Variables */
:root {
  /* Colors */
  --primary: #007bff;
  --primary-dark: #0056b3;
  --secondary: #28a745;
  --accent: #ffc107;
  --danger: #dc3545;

  /* Light Theme */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #adb5bd;
  --border: #dee2e6;
  --shadow: rgba(0, 0, 0, 0.1);
  --shadow-lg: rgba(0, 0, 0, 0.15);

  /* Typography */
  --font-primary: 'Kanit', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-secondary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  /* Spacing */
  --container-max-width: 1200px;
  --section-padding: 5rem 0;
  --border-radius: 12px;
  --border-radius-sm: 8px;

  /* Transitions */
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s ease;
}

/* Dark Theme */
body.dark {
  --bg-primary: #0d1117;
  --bg-secondary: #161b22;
  --bg-tertiary: #21262d;
  --text-primary: #f0f6fc;
  --text-secondary: #8b949e;
  --text-muted: #6e7681;
  --border: #30363d;
  --shadow: rgba(0, 0, 0, 0.3);
  --shadow-lg: rgba(0, 0, 0, 0.4);
  --primary: #1f6feb;
  --primary-dark: #1158c7;
}

/* Reset & Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  transition: var(--transition);
  overflow-x: hidden;
}

/* Container */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 768px) {
  .container {
    padding: 0 2rem;
  }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 { font-size: clamp(2rem, 5vw, 3.5rem); }
h2 { font-size: clamp(1.75rem, 4vw, 2.5rem); }
h3 { font-size: clamp(1.25rem, 3vw, 1.75rem); }
h4 { font-size: 1.25rem; }

p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
}

/* Loading Screen */
#loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

#loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid var(--border);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border);
  z-index: 1000;
  transition: var(--transition);
}

body.dark .header {
  background: rgba(13, 17, 23, 0.95);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  font-size: 1.25rem;
  color: var(--text-primary);
}

.logo img {
  height: 40px;
  width: auto;
}

.logo-text {
  display: none;
}

@media (min-width: 768px) {
  .logo-text {
    display: block;
  }
}

/* Navigation */
.nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-links {
  display: none;
  list-style: none;
  gap: 2rem;
  margin: 0;
}

.nav-links a {
  color: var(--text-primary);
  font-weight: 500;
  padding: 0.5rem 0;
  position: relative;
  transition: var(--transition-fast);
}

.nav-links a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary);
  transition: width 0.3s ease;
}

.nav-links a:hover::after,
.nav-links a.active::after {
  width: 100%;
}

@media (min-width: 768px) {
  .nav-links {
    display: flex;
  }
}

/* Navigation Controls */
.nav-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.theme-toggle,
.lang-toggle {
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: var(--border-radius-sm);
  padding: 0.5rem;
  cursor: pointer;
  transition: var(--transition-fast);
  font-size: 0.875rem;
  font-weight: 500;
}

.theme-toggle:hover,
.lang-toggle:hover {
  background: var(--bg-tertiary);
  transform: translateY(-1px);
}

.theme-toggle {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lang-toggle {
  min-width: 50px;
  color: var(--text-primary);
}

/* Mobile Menu */
.mobile-menu-btn {
  display: flex;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

.mobile-menu-btn span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  border-radius: 2px;
  transition: var(--transition-fast);
}

.mobile-menu-btn.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-btn.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-btn.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

@media (min-width: 768px) {
  .mobile-menu-btn {
    display: none;
  }
}

/* Mobile Navigation */
@media (max-width: 767px) {
  .nav {
    position: fixed;
    top: 100%;
    left: 0;
    width: 100%;
    background: var(--bg-primary);
    border-top: 1px solid var(--border);
    padding: 2rem 1rem;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    flex-direction: column;
    gap: 1rem;
  }

  .nav.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }

  .nav-links a {
    padding: 1rem;
    border-radius: var(--border-radius-sm);
    background: var(--bg-secondary);
    text-align: center;
  }
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding-top: 80px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  z-index: -2;
}

.hero-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, var(--primary) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, var(--secondary) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, var(--accent) 0%, transparent 50%);
  opacity: 0.1;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(-10px) rotate(-1deg); }
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  align-items: center;
  width: 100%;
}

@media (min-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }
}

.hero-text {
  text-align: center;
}

@media (min-width: 768px) {
  .hero-text {
    text-align: left;
  }
}

.hero-title {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 700;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: slideInUp 1s ease-out;
}

.hero-subtitle {
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  margin-bottom: 2rem;
  color: var(--text-secondary);
  animation: slideInUp 1s ease-out 0.2s both;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
  animation: slideInUp 1s ease-out 0.4s both;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border);
  transition: var(--transition);
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px var(--shadow);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
  animation: slideInUp 1s ease-out 0.6s both;
}

@media (min-width: 480px) {
  .hero-buttons {
    flex-direction: row;
    justify-content: center;
  }

  @media (min-width: 768px) {
    .hero-buttons {
      justify-content: flex-start;
    }
  }
}

/* CTA Buttons */
.cta {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.cta.primary {
  background: var(--primary);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.cta.primary:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
}

.cta.secondary {
  background: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
}

.cta.secondary:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-2px);
}

/* Hero Visual */
.hero-visual {
  position: relative;
  animation: slideInRight 1s ease-out 0.8s both;
}

#truck-path {
  width: 100%;
  height: 400px;
  position: relative;
}

#truck-path svg {
  width: 100%;
  height: 100%;
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

.scroll-arrow {
  width: 30px;
  height: 30px;
  border: 2px solid var(--primary);
  border-top: none;
  border-right: none;
  transform: rotate(-45deg);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
  40% { transform: translateX(-50%) translateY(-10px); }
  60% { transform: translateX(-50%) translateY(-5px); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
/* Section Styles */
section {
  padding: var(--section-padding);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Services Section */
.services {
  background: var(--bg-secondary);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.service-card {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--border);
  transition: var(--transition);
  text-align: center;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px var(--shadow-lg);
}

.service-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.service-icon svg {
  width: 40px;
  height: 40px;
}

.service-card h3 {
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.service-card p {
  margin-bottom: 1.5rem;
}

.service-features {
  list-style: none;
  text-align: left;
}

.service-features li {
  padding: 0.5rem 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: 1.5rem;
}

.service-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--secondary);
  font-weight: bold;
}

/* Why Choose Section */
.why-choose {
  background: var(--bg-primary);
}

.why-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.why-card {
  background: var(--bg-secondary);
  padding: 2rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--border);
  transition: var(--transition);
  text-align: center;
}

.why-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px var(--shadow-lg);
}

.why-card h3 {
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-size: 1.25rem;
}

.why-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* About Section */
.about-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 4rem;
  align-items: center;
}

@media (min-width: 768px) {
  .about-content {
    grid-template-columns: 1fr 1fr;
  }
}

.about-description {
  font-size: 1.125rem;
  margin-bottom: 2rem;
}

.about-features {
  display: grid;
  gap: 1rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.feature-icon {
  width: 30px;
  height: 30px;
  background: var(--secondary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.875rem;
}

.about-visual {
  position: relative;
}

.about-image {
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 20px 40px var(--shadow-lg);
}

.image-placeholder {
  width: 100%;
  height: 300px;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-placeholder svg {
  width: 100%;
  height: 100%;
}

/* Clients Section */
.clients {
  background: var(--bg-secondary);
}

.clients-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.client-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border);
  transition: var(--transition);
}

.client-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px var(--shadow);
}

.client-logo img {
  max-width: 100%;
  max-height: 60px;
  object-fit: contain;
  filter: grayscale(100%);
  transition: var(--transition);
}

.client-item:hover .client-logo img {
  filter: grayscale(0%);
}

/* Testimonials */
.testimonials {
  margin-top: 4rem;
}

.testimonial-slider {
  max-width: 800px;
  margin: 0 auto;
}

.testimonial-item {
  text-align: center;
  padding: 2rem;
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border);
}

.testimonial-content p {
  font-size: 1.125rem;
  font-style: italic;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.testimonial-author strong {
  color: var(--text-primary);
  display: block;
  margin-bottom: 0.5rem;
}

.testimonial-author span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}
/* Contact Section */
.contact-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 4rem;
  align-items: start;
}

@media (min-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr 1fr;
  }
}

.contact-description {
  font-size: 1.125rem;
  margin-bottom: 2rem;
}

.contact-details {
  display: grid;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: var(--primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-icon svg {
  width: 24px;
  height: 24px;
}

.contact-text h4 {
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.contact-text p {
  margin: 0;
  color: var(--text-secondary);
}

/* Contact Form */
.contact-form {
  background: var(--bg-secondary);
  padding: 2rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--border);
}

.form-group {
  position: relative;
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border);
  border-radius: var(--border-radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: var(--transition);
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-group label {
  position: absolute;
  top: 1rem;
  left: 1rem;
  color: var(--text-secondary);
  font-size: 1rem;
  transition: var(--transition);
  pointer-events: none;
  background: var(--bg-primary);
  padding: 0 0.25rem;
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label,
.form-group textarea:focus + label,
.form-group textarea:not(:placeholder-shown) + label {
  top: -0.5rem;
  left: 0.75rem;
  font-size: 0.875rem;
  color: var(--primary);
}

.submit-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.submit-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-loading {
  display: none;
}

.submit-btn.loading .btn-text {
  display: none;
}

.submit-btn.loading .btn-loading {
  display: block;
}

/* Footer */
.footer {
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border);
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h4 {
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.footer-logo img {
  height: 40px;
  width: auto;
}

.footer-logo span {
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--text-primary);
}

.footer-desc {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section ul li a {
  color: var(--text-secondary);
  transition: var(--transition-fast);
}

.footer-section ul li a:hover {
  color: var(--primary);
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-links a {
  width: 40px;
  height: 40px;
  background: var(--bg-secondary);
  border: 1px solid var(--border);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  font-size: 1.25rem;
}

.social-links a:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-2px);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid var(--border);
}

.footer-bottom p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top:hover {
  background: var(--primary-dark);
  transform: translateY(-5px);
}

.back-to-top svg {
  width: 24px;
  height: 24px;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }

/* Responsive Utilities */
@media (max-width: 767px) {
  .hide-mobile { display: none; }
}

@media (min-width: 768px) {
  .hide-desktop { display: none; }
}

/* AOS Animation Overrides */
[data-aos] {
  pointer-events: none;
}

[data-aos].aos-animate {
  pointer-events: auto;
}