# ระบบความปลอดภัยของฟอร์มติดต่อ / Contact Form Security System

## การป้องกันที่ได้เพิ่มเข้ามา / Security Features Added

### 1. การป้องกันการโจมตี Injection
- **SQL Injection Protection**: ใช้ `htmlspecialchars()` และ validation patterns
- **XSS Protection**: กรองและ escape ข้อมูลทั้งฝั่ง frontend และ backend
- **Script Injection**: ตรวจจับและบล็อก JavaScript code ในข้อมูล

### 2. CSRF Protection
- **CSRF Token**: สร้าง token สำหรับแต่ละ session
- **Token Validation**: ตรวจสอบ token ทุกครั้งที่ส่งฟอร์ม
- **Session Management**: จัดการ session อย่างปลอดภัย

### 3. Rate Limiting
- **IP-based Limiting**: จำกัดการส่งฟอร์มไม่เกิน 5 ครั้งต่อชั่วโมงต่อ IP
- **Time Window**: ใช้ระบบ sliding window 1 ชั่วโมง
- **Automatic Cleanup**: ลบข้อมูลเก่าอัตโนมัติ

### 4. Spam Protection
- **Honeypot Field**: ฟิลด์ซ่อนสำหรับดัก bot
- **URL Detection**: ตรวจจับและบล็อก URL ในข้อความ
- **Suspicious Pattern Detection**: ตรวจจับคำและรูปแบบที่น่าสงสัย
- **Content Filtering**: กรองเนื้อหาที่ไม่เหมาะสม

### 5. Input Validation
- **Frontend Validation**: ตรวจสอบข้อมูลแบบ real-time
- **Backend Validation**: ตรวจสอบข้อมูลอีกครั้งฝั่งเซิร์ฟเวอร์
- **Data Sanitization**: ทำความสะอาดข้อมูลก่อนประมวลผล
- **Length Limits**: จำกัดความยาวของข้อมูล

### 6. Security Headers
- **X-Content-Type-Options**: ป้องกัน MIME type sniffing
- **X-Frame-Options**: ป้องกัน clickjacking
- **X-XSS-Protection**: เปิดใช้งาน XSS protection
- **Content-Security-Policy**: จำกัดแหล่งที่มาของ content

## ไฟล์ที่ได้รับการปรับปรุง / Modified Files

### 1. `send_mail.php`
- เพิ่มระบบ CSRF protection
- เพิ่ม rate limiting
- เพิ่ม input validation และ sanitization
- เพิ่ม honeypot detection
- เพิ่ม security headers

### 2. `js/app.js`
- เพิ่ม client-side validation
- เพิ่ม CSRF token management
- เพิ่ม real-time form validation
- เพิ่ม error handling และ user feedback
- เพิ่ม honeypot handling

### 3. `index.html`
- เพิ่ม input validation attributes
- เพิ่ม honeypot field
- เพิ่ม security-related form attributes
- เพิ่ม rate limiting notice

### 4. `assets/style.css`
- เพิ่ม styles สำหรับ error states
- เพิ่ม validation feedback styles
- เพิ่ม loading animations
- เพิ่ม honeypot field hiding

### 5. `.htaccess`
- เพิ่ม security headers
- เพิ่ม protection จาก common attacks
- เพิ่ม file access restrictions
- เพิ่ม performance optimizations

## การใช้งาน / Usage

### การตั้งค่าเริ่มต้น / Initial Setup
1. ตรวจสอบให้แน่ใจว่าเซิร์ฟเวอร์รองรับ PHP sessions
2. ตรวจสอบสิทธิ์การเขียนไฟล์สำหรับ `rate_limit.json`
3. ปรับแต่งค่า rate limiting ตามความต้องการ

### การปรับแต่ง / Customization
```php
// ใน send_mail.php
$max_requests = 5;        // จำนวนครั้งสูงสุดต่อ IP
$time_window = 3600;      // ช่วงเวลา (วินาที)
```

### การตรวจสอบ / Monitoring
- ตรวจสอบไฟล์ `rate_limit.json` สำหรับ rate limiting data
- ตรวจสอบ error logs สำหรับ honeypot triggers
- ตรวจสอบ server logs สำหรับ blocked requests

## การทดสอบ / Testing

### ทดสอบ Rate Limiting
1. ส่งฟอร์มมากกว่า 5 ครั้งภายใน 1 ชั่วโมง
2. ควรได้รับข้อความ "Too many requests"

### ทดสอบ CSRF Protection
1. ลองส่งฟอร์มโดยไม่มี CSRF token
2. ควรได้รับข้อความ "Invalid security token"

### ทดสอบ Honeypot
1. กรอกข้อมูลในฟิลด์ "website" (ซ่อน)
2. ควรได้รับข้อความ success แต่อีเมลจะไม่ถูกส่ง

### ทดสอบ Input Validation
1. ลองกรอกข้อมูลที่ไม่ถูกต้อง (เช่น อีเมลผิดรูปแบบ)
2. ควรเห็น error messages แบบ real-time

## ข้อควรระวัง / Important Notes

1. **Rate Limiting File**: ไฟล์ `rate_limit.json` จะถูกสร้างอัตโนมัติ
2. **Session Requirements**: ต้องการ PHP sessions สำหรับ CSRF protection
3. **JavaScript Required**: ระบบต้องการ JavaScript เพื่อทำงานเต็มรูปแบบ
4. **Server Configuration**: บาง security headers อาจต้องการการตั้งค่าเซิร์ฟเวอร์เพิ่มเติม

## การบำรุงรักษา / Maintenance

### ทำความสะอาดข้อมูล / Data Cleanup
- ไฟล์ `rate_limit.json` จะทำความสะอาดข้อมูลเก่าอัตโนมัติ
- ตรวจสอบขนาดไฟล์เป็นระยะ

### การอัปเดต / Updates
- ตรวจสอบและอัปเดต suspicious patterns เป็นระยะ
- ปรับแต่งค่า rate limiting ตามการใช้งานจริง
- ตรวจสอบ security headers ให้ทันสมัย

## การแก้ไขปัญหา / Troubleshooting

### ปัญหาที่พบบ่อย / Common Issues
1. **CSRF Token Error**: รีเฟรชหน้าเว็บ
2. **Rate Limit Error**: รอ 1 ชั่วโมงหรือลบไฟล์ `rate_limit.json`
3. **Validation Error**: ตรวจสอบรูปแบบข้อมูลที่กรอก
4. **Email Not Sent**: ตรวจสอบการตั้งค่า mail server

### การดีบัก / Debugging
- เปิด browser developer tools เพื่อดู console errors
- ตรวจสอบ PHP error logs
- ตรวจสอบ network requests ใน browser
