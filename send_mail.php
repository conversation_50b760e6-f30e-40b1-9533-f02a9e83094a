<?php
session_start();

// Security headers
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Rate limiting configuration
$rate_limit_file = 'rate_limit.json';
$max_requests = 5; // Maximum requests per IP
$time_window = 3600; // Time window in seconds (1 hour)

// CSRF Protection
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Rate limiting function
function checkRateLimit($ip) {
    global $rate_limit_file, $max_requests, $time_window;

    $rate_data = [];
    if (file_exists($rate_limit_file)) {
        $rate_data = json_decode(file_get_contents($rate_limit_file), true) ?: [];
    }

    $current_time = time();

    // Clean old entries
    foreach ($rate_data as $stored_ip => $data) {
        if ($current_time - $data['first_request'] > $time_window) {
            unset($rate_data[$stored_ip]);
        }
    }

    // Check current IP
    if (!isset($rate_data[$ip])) {
        $rate_data[$ip] = [
            'count' => 1,
            'first_request' => $current_time
        ];
    } else {
        $rate_data[$ip]['count']++;
    }

    // Save updated data
    file_put_contents($rate_limit_file, json_encode($rate_data));

    return $rate_data[$ip]['count'] <= $max_requests;
}

// Input validation and sanitization
function validateAndSanitizeInput($data) {
    $errors = [];

    // Name validation
    $name = trim($data['name'] ?? '');
    if (empty($name)) {
        $errors[] = 'Name is required';
    } elseif (strlen($name) < 2 || strlen($name) > 100) {
        $errors[] = 'Name must be between 2 and 100 characters';
    } elseif (!preg_match('/^[a-zA-Zก-๙\s\-\.]+$/u', $name)) {
        $errors[] = 'Name contains invalid characters';
    }

    // Email validation
    $email = trim($data['email'] ?? '');
    if (empty($email)) {
        $errors[] = 'Email is required';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    } elseif (strlen($email) > 254) {
        $errors[] = 'Email is too long';
    }

    // Phone validation (optional)
    $phone = trim($data['phone'] ?? '');
    if (!empty($phone)) {
        // ตรวจสอบว่าเป็นตัวเลขเท่านั้น
        if (!preg_match('/^[0-9]+$/', $phone)) {
            $errors[] = 'Phone number must contain only numbers';
        }
        // ตรวจสอบความยาว
        if (strlen($phone) < 8 || strlen($phone) > 15) {
            $errors[] = 'Phone number must be between 8-15 digits';
        }
    }

    // Message validation
    $message = trim($data['message'] ?? '');
    if (empty($message)) {
        $errors[] = 'Message is required';
    } elseif (strlen($message) < 10 || strlen($message) > 2000) {
        $errors[] = 'Message must be between 10 and 2000 characters';
    }

    // Check for suspicious patterns
    $suspicious_patterns = [
        '/\b(viagra|cialis|casino|poker|lottery|winner|congratulations)\b/i',
        '/<script[^>]*>.*?<\/script>/is',
        '/javascript:/i',
        '/on\w+\s*=/i',
        '/\b(http|https|ftp):\/\/[^\s]+/i' // Block URLs in message
    ];

    foreach ($suspicious_patterns as $pattern) {
        if (preg_match($pattern, $name . ' ' . $email . ' ' . $message)) {
            $errors[] = 'Suspicious content detected';
            break;
        }
    }

    return [
        'valid' => empty($errors),
        'errors' => $errors,
        'data' => [
            'name' => htmlspecialchars($name, ENT_QUOTES, 'UTF-8'),
            'email' => htmlspecialchars($email, ENT_QUOTES, 'UTF-8'),
            'phone' => htmlspecialchars($phone, ENT_QUOTES, 'UTF-8'),
            'message' => htmlspecialchars($message, ENT_QUOTES, 'UTF-8')
        ]
    ];
}

// Handle GET request for CSRF token
if ($_SERVER["REQUEST_METHOD"] === "GET") {
    echo json_encode(['csrf_token' => generateCSRFToken()]);
    exit;
}

// Handle POST request
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $client_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

    // Honeypot check (spam protection)
    $honeypot = $_POST['website'] ?? '';
    if (!empty(trim($honeypot))) {
        // This is likely a bot, log and return fake success
        error_log("Honeypot triggered from IP: $client_ip");
        echo json_encode(['success' => true, 'message' => 'Message sent successfully!']);
        exit;
    }

    // Check rate limiting
    if (!checkRateLimit($client_ip)) {
        http_response_code(429);
        echo json_encode(['error' => 'Too many requests. Please try again later.']);
        exit;
    }

    // Validate CSRF token
    $csrf_token = $_POST['csrf_token'] ?? '';
    if (!validateCSRFToken($csrf_token)) {
        http_response_code(403);
        echo json_encode(['error' => 'Invalid security token. Please refresh the page.']);
        exit;
    }

    // Validate and sanitize input
    $validation = validateAndSanitizeInput($_POST);

    if (!$validation['valid']) {
        http_response_code(400);
        echo json_encode(['error' => 'Validation failed', 'details' => $validation['errors']]);
        exit;
    }

    $data = $validation['data'];

    // Prepare email
    $to = "<EMAIL>";
    $subject = "New contact form message from " . $data['name'];
    $body = "Name: " . $data['name'] . "\n";
    $body .= "Email: " . $data['email'] . "\n";
    if (!empty($data['phone'])) {
        $body .= "Phone: " . $data['phone'] . "\n";
    }
    $body .= "\nMessage:\n" . $data['message'];
    $body .= "\n\n---\n";
    $body .= "IP Address: " . $client_ip . "\n";
    $body .= "User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'Unknown') . "\n";
    $body .= "Timestamp: " . date('Y-m-d H:i:s');

    // Set proper headers
    $headers = [
        'From: <EMAIL>',
        'Reply-To: ' . $data['email'],
        'X-Mailer: PHP/' . phpversion(),
        'Content-Type: text/plain; charset=UTF-8'
    ];

    // Send email
    if (mail($to, $subject, $body, implode("\r\n", $headers))) {
        echo json_encode(['success' => true, 'message' => 'Message sent successfully!']);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to send message. Please try again later.']);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}
?>