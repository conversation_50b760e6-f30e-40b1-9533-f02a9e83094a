# Security Headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Prevent clickjacking
    Header always set X-Frame-Options DENY
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://unpkg.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self'"
    
    # Remove server signature
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# Hide sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|json)$">
    Order Allow,Deny
    <PERSON> from all
</FilesMatch>

# Protect rate_limit.json file
<Files "rate_limit.json">
    Order Allow,Deny
    Deny from all
</Files>

# Prevent access to PHP error logs
<Files "error_log">
    Order Allow,Deny
    Deny from all
</Files>

# Block suspicious requests
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block requests with suspicious user agents
    RewriteCond %{HTTP_USER_AGENT} (libwww-perl|wget|python|nikto|curl|scan|java|winhttp|clshttp|loader) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (%0A|%0D|%27|%3C|%3E|%00) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (;|<|>|'|"|\)|\(|%0A|%0D|%22|%27|%28|%3C|%3E|%00).*(libwww-perl|wget|python|nikto|curl|scan|java|winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC]
    RewriteRule .* - [F,L]
    
    # Block SQL injection attempts
    RewriteCond %{QUERY_STRING} (union.*select|insert.*into|delete.*from|drop.*table) [NC]
    RewriteRule .* - [F,L]
    
    # Block XSS attempts
    RewriteCond %{QUERY_STRING} (<script|javascript:|vbscript:|onload|onerror|onclick) [NC]
    RewriteRule .* - [F,L]
    
    # Block file inclusion attempts
    RewriteCond %{QUERY_STRING} (\.\.\/|\.\.\\|\/etc\/passwd|\/proc\/self\/environ) [NC]
    RewriteRule .* - [F,L]
    
    # Block common exploit attempts
    RewriteCond %{QUERY_STRING} (base64_encode|base64_decode|benchmark|sleep|waitfor) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# Rate limiting for form submissions (basic)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        5
    DOSPageInterval     60
    DOSSiteCount        50
    DOSSiteInterval     60
    DOSBlockingPeriod   3600
</IfModule>

# Disable server signature
ServerSignature Off

# Disable directory browsing
Options -Indexes

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>
