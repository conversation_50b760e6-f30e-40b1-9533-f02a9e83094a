<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบระบบความปลอดภัยฟอร์ม</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .danger {
            border-left-color: #dc3545;
        }
        .warning {
            border-left-color: #ffc107;
        }
        .success {
            border-left-color: #28a745;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .code {
            background: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔒 ทดสอบระบบความปลอดภัยฟอร์มติดต่อ</h1>
    
    <div class="test-section">
        <h2 class="test-title">1. ทดสอบ Input Validation</h2>
        
        <div class="test-case danger">
            <h3>🚫 ทดสอบ XSS Attack</h3>
            <p>ลองกรอก JavaScript code ในฟิลด์ข้อความ:</p>
            <div class="code">&lt;script&gt;alert('XSS Attack!')&lt;/script&gt;</div>
            <p><strong>ผลที่คาดหวัง:</strong> ระบบจะตรวจจับและบล็อกข้อมูลนี้</p>
        </div>

        <div class="test-case danger">
            <h3>🚫 ทดสอบ SQL Injection</h3>
            <p>ลองกรอกคำสั่ง SQL ในฟิลด์ชื่อ:</p>
            <div class="code">'; DROP TABLE users; --</div>
            <p><strong>ผลที่คาดหวัง:</strong> ระบบจะ sanitize ข้อมูลและป้องกันการโจมตี</p>
        </div>

        <div class="test-case warning">
            <h3>⚠️ ทดสอบ URL ในข้อความ</h3>
            <p>ลองกรอก URL ในฟิลด์ข้อความ:</p>
            <div class="code">ติดต่อเราที่ https://spam-site.com</div>
            <p><strong>ผลที่คาดหวัง:</strong> ระบบจะตรวจจับและปฏิเสธข้อความที่มี URL</p>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">2. ทดสอบ Rate Limiting</h2>
        
        <div class="test-case warning">
            <h3>⚠️ ทดสอบการส่งฟอร์มซ้ำ</h3>
            <p>ลองส่งฟอร์มมากกว่า 5 ครั้งภายใน 1 ชั่วโมง</p>
            <button class="test-button" onclick="testRateLimit()">ทดสอบ Rate Limiting</button>
            <div id="rateLimitResult"></div>
            <p><strong>ผลที่คาดหวัง:</strong> หลังจากส่ง 5 ครั้ง จะได้รับข้อความ "Too many requests"</p>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">3. ทดสอบ Honeypot Protection</h2>
        
        <div class="test-case success">
            <h3>🍯 ทดสอบ Bot Detection</h3>
            <p>ระบบมีฟิลด์ซ่อน "website" ที่มนุษย์จะไม่เห็น แต่ bot จะกรอกข้อมูล</p>
            <button class="test-button" onclick="testHoneypot()">ทดสอบ Honeypot</button>
            <div id="honeypotResult"></div>
            <p><strong>ผลที่คาดหวัง:</strong> หาก bot กรอกฟิลด์นี้ จะได้รับ fake success แต่อีเมลจะไม่ถูกส่ง</p>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">4. ทดสอบ CSRF Protection</h2>
        
        <div class="test-case success">
            <h3>🔐 ทดสอบ Security Token</h3>
            <p>ระบบใช้ CSRF token เพื่อป้องกันการโจมตีจากเว็บไซต์อื่น</p>
            <button class="test-button" onclick="testCSRF()">ทดสอบ CSRF Token</button>
            <div id="csrfResult"></div>
            <p><strong>ผลที่คาดหวัง:</strong> การส่งฟอร์มโดยไม่มี token จะถูกปฏิเสธ</p>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">5. การตรวจสอบความปลอดภัย</h2>
        
        <div class="test-case success">
            <h3>✅ ฟีเจอร์ความปลอดภัยที่เปิดใช้งาน</h3>
            <ul>
                <li>✅ Input Validation & Sanitization</li>
                <li>✅ XSS Protection</li>
                <li>✅ SQL Injection Prevention</li>
                <li>✅ CSRF Token Protection</li>
                <li>✅ Rate Limiting (5 requests/hour)</li>
                <li>✅ Honeypot Spam Protection</li>
                <li>✅ URL Detection in Messages</li>
                <li>✅ Suspicious Pattern Detection</li>
                <li>✅ Security Headers</li>
                <li>✅ Real-time Form Validation</li>
            </ul>
        </div>
    </div>

    <script>
        let testCount = 0;

        async function testRateLimit() {
            const resultDiv = document.getElementById('rateLimitResult');
            resultDiv.innerHTML = '<p>กำลังทดสอบ...</p>';
            
            try {
                // ส่งคำขอหลายครั้งเพื่อทดสอบ rate limiting
                for (let i = 1; i <= 6; i++) {
                    const formData = new FormData();
                    formData.append('name', 'Test User ' + i);
                    formData.append('email', '<EMAIL>');
                    formData.append('message', 'This is a rate limit test #' + i);
                    
                    const response = await fetch('send_mail.php', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    if (response.status === 429) {
                        resultDiv.innerHTML = `<div class="result success">✅ Rate limiting ทำงานถูกต้อง! ถูกบล็อกที่ครั้งที่ ${i}</div>`;
                        return;
                    }
                    
                    // รอสักครู่ระหว่างการส่ง
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
                resultDiv.innerHTML = '<div class="result error">❌ Rate limiting อาจไม่ทำงาน หรือยังไม่ถึงขีดจำกัด</div>';
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ เกิดข้อผิดพลาด: ${error.message}</div>`;
            }
        }

        async function testHoneypot() {
            const resultDiv = document.getElementById('honeypotResult');
            resultDiv.innerHTML = '<p>กำลังทดสอบ...</p>';
            
            try {
                const formData = new FormData();
                formData.append('name', 'Test Bot');
                formData.append('email', '<EMAIL>');
                formData.append('message', 'This is a honeypot test');
                formData.append('website', 'http://spam-site.com'); // Honeypot field
                
                const response = await fetch('send_mail.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = '<div class="result success">✅ Honeypot ทำงานถูกต้อง! Bot ได้รับ fake success</div>';
                } else {
                    resultDiv.innerHTML = '<div class="result error">❌ Honeypot อาจไม่ทำงานถูกต้อง</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ เกิดข้อผิดพลาด: ${error.message}</div>`;
            }
        }

        async function testCSRF() {
            const resultDiv = document.getElementById('csrfResult');
            resultDiv.innerHTML = '<p>กำลังทดสอบ...</p>';
            
            try {
                // ส่งฟอร์มโดยไม่มี CSRF token
                const formData = new FormData();
                formData.append('name', 'Test User');
                formData.append('email', '<EMAIL>');
                formData.append('message', 'This is a CSRF test');
                // ไม่ส่ง csrf_token
                
                const response = await fetch('send_mail.php', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.status === 403) {
                    resultDiv.innerHTML = '<div class="result success">✅ CSRF Protection ทำงานถูกต้อง! คำขอถูกปฏิเสธ</div>';
                } else {
                    resultDiv.innerHTML = '<div class="result error">❌ CSRF Protection อาจไม่ทำงานถูกต้อง</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ เกิดข้อผิดพลาด: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
